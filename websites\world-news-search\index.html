<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Western News Map</title>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=IM+Fell+English:ital,wght@0,400;1,400&display=swap" rel="stylesheet">

    <!-- Simple dependencies -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <style>
        :root {
            --rdr-parchment: #D8C7A0;
            --rdr-border: #3A2E24;
            --rdr-accent: #4E5A3E;
            --rdr-highlight: #d4a373;
        }

        body {
            margin: 0;
            padding: 20px;
            font-family: 'IM Fell English', serif;
            background: linear-gradient(135deg, var(--rdr-parchment) 0%, #C8B790 100%);
            color: var(--rdr-border);
            min-height: 100vh;
        }

        .title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .map-container {
            width: 100%;
            max-width: 1000px;
            margin: 0 auto;
            background: var(--rdr-parchment);
            border: 4px solid var(--rdr-border);
            border-radius: 15px;
            padding: 20px;
            box-shadow: inset 0 0 30px rgba(58, 46, 36, 0.2);
        }

        .world-map {
            width: 100%;
            height: 500px;
            cursor: crosshair;
        }

        .country {
            fill: rgba(216, 199, 160, 0.3);
            stroke: var(--rdr-border);
            stroke-width: 2;
            stroke-dasharray: 5,5;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .country:hover {
            fill: var(--rdr-highlight);
            stroke-width: 3;
            stroke-dasharray: none;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            font-size: 1.2rem;
        }

        .news-container {
            margin-top: 2rem;
            max-height: 400px;
            overflow-y: auto;
            background: rgba(255,255,255,0.1);
            border: 2px solid var(--rdr-border);
            border-radius: 10px;
            padding: 1rem;
        }

        .news-item {
            background: rgba(255,255,255,0.3);
            border: 1px solid var(--rdr-border);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .news-item:hover {
            background: rgba(255,255,255,0.5);
            transform: translateY(-2px);
        }

        .news-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--rdr-border);
        }

        .news-snippet {
            font-size: 0.9rem;
            line-height: 1.4;
        }

        /* Western Photo Frame */
        .photo-frame {
            position: fixed;
            z-index: 1000;
            border: 15px solid #8B4513;
            border-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30"><rect width="30" height="30" fill="%238B4513"/><rect x="5" y="5" width="20" height="20" fill="none" stroke="%23654321" stroke-width="2"/></svg>') 15;
            background: #8B4513;
            box-shadow:
                0 0 20px rgba(0,0,0,0.5),
                inset 0 0 10px rgba(0,0,0,0.3);
            cursor: pointer;
            transition: all 0.3s ease;
            max-width: 300px;
            max-height: 300px;
        }

        .photo-frame:hover {
            transform: scale(1.05);
            box-shadow: 0 0 30px rgba(0,0,0,0.7);
        }

        .photo-frame img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }

        .photo-description {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(58, 46, 36, 0.95);
            color: var(--rdr-parchment);
            padding: 1rem 2rem;
            border-radius: 10px;
            max-width: 80%;
            text-align: center;
            z-index: 1001;
            border: 2px solid var(--rdr-highlight);
        }

        .close-btn {
            position: absolute;
            top: -10px;
            right: -10px;
            background: var(--rdr-border);
            color: var(--rdr-parchment);
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
            font-size: 1.2rem;
        }
    </style>

        .parchment-texture {
            position: relative;
        }

        .parchment-texture::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(139, 69, 19, 0.05) 0%, transparent 2%),
                radial-gradient(circle at 75% 75%, rgba(139, 69, 19, 0.05) 0%, transparent 2%);
            background-size: 50px 50px;
            pointer-events: none;
            z-index: 1;
        }

        /* Enhanced Header with RDR2 Styling */
        .frontier-header {
            text-align: center;
            padding: 2rem 1rem;
            background: linear-gradient(180deg, rgba(58, 46, 36, 0.1) 0%, transparent 100%);
            border-bottom: 3px double var(--rdr-border);
            position: relative;
        }

        .frontier-title {
            font-family: 'Cinzel', serif;
            font-size: clamp(2rem, 5vw, 4rem);
            color: var(--rdr-border);
            text-shadow:
                2px 2px 0px var(--rdr-highlight),
                4px 4px 8px var(--rdr-shadow);
            margin: 0;
            letter-spacing: 2px;
            font-weight: 600;
        }

        .frontier-subtitle {
            font-family: 'IM Fell English', serif;
            font-size: clamp(1rem, 2.5vw, 1.5rem);
            color: var(--rdr-text);
            margin: 0.5rem 0 0 0;
            font-style: italic;
        }

        .telegraph-line {
            height: 2px;
            background: repeating-linear-gradient(
                90deg,
                var(--rdr-border) 0px,
                var(--rdr-border) 10px,
                transparent 10px,
                transparent 20px
            );
            margin: 1rem auto;
            width: 60%;
        }

        /* Enhanced Map Container for Leaflet */
        .world-map-container {
            position: relative;
            width: 100%;
            height: 70vh;
            min-height: 500px;
            background:
                radial-gradient(ellipse at center, rgba(212, 163, 115, 0.1) 0%, transparent 70%),
                linear-gradient(180deg, rgba(58, 46, 36, 0.05) 0%, rgba(78, 90, 62, 0.05) 100%);
            border: 4px solid var(--rdr-border);
            border-radius: 15px;
            margin: 2rem auto;
            max-width: 1200px;
            box-shadow:
                inset 0 0 50px rgba(58, 46, 36, 0.2),
                0 10px 30px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        /* Leaflet Map Styling */
        .leaflet-container {
            width: 100%;
            height: 100%;
            background: transparent;
            font-family: 'IM Fell English', serif;
        }

        .leaflet-control-zoom {
            border: 2px solid var(--rdr-border) !important;
            border-radius: 8px !important;
        }

        .leaflet-control-zoom a {
            background: var(--rdr-parchment) !important;
            color: var(--rdr-border) !important;
            border: none !important;
            font-family: 'IM Fell English', serif !important;
            font-weight: bold !important;
        }

        .leaflet-control-zoom a:hover {
            background: var(--rdr-highlight) !important;
        }

        /* Country Boundaries Styling */
        .leaflet-interactive {
            stroke: var(--rdr-border);
            stroke-width: 2;
            stroke-dasharray: 5,5;
            fill: rgba(216, 199, 160, 0.3);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .leaflet-interactive:hover {
            fill: rgba(212, 163, 115, 0.5);
            stroke-width: 3;
            stroke-dasharray: none;
            filter: drop-shadow(0 0 10px rgba(212, 163, 115, 0.8));
        }

        .news-marker {
            fill: var(--frontier-blood);
            stroke: var(--frontier-gold);
            stroke-width: 2;
            r: 4;
            opacity: 0.8;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .news-marker:hover {
            r: 8;
            opacity: 1;
            filter: drop-shadow(0 0 8px var(--frontier-blood));
        }

        .news-marker.pulsing {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { r: 4; opacity: 0.8; }
            50% { r: 6; opacity: 1; }
            100% { r: 4; opacity: 0.8; }
        }

        /* Enhanced Collapsible Telegraph Sidebar */
        .telegraph-sidebar {
            position: fixed;
            right: -420px;
            top: 0;
            width: 420px;
            height: 100vh;
            background:
                linear-gradient(135deg, var(--rdr-parchment) 0%, #C8B790 50%, #B8A780 100%);
            border-left: 4px solid var(--rdr-border);
            box-shadow: -10px 0 30px rgba(0, 0, 0, 0.4);
            transition: right 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            z-index: 1000;
            overflow-y: auto;
        }

        .telegraph-sidebar.open {
            right: 0;
        }

        /* Telegraph Key Toggle */
        .telegraph-toggle {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: var(--rdr-border);
            color: var(--rdr-parchment);
            border: none;
            width: 50px;
            height: 80px;
            border-radius: 25px 0 0 25px;
            cursor: pointer;
            font-size: 1.5rem;
            transition: all 0.3s ease;
            z-index: 999;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
        }

        .telegraph-toggle:hover {
            background: var(--rdr-highlight);
            transform: translateY(-50%) translateX(-5px);
        }

        .telegraph-toggle.active {
            right: 420px;
        }

        .sidebar-header {
            background: var(--rdr-border);
            color: var(--rdr-parchment);
            padding: 1.5rem;
            position: relative;
        }

        .sidebar-title {
            font-family: 'Cinzel', serif;
            font-size: 1.5rem;
            margin: 0;
            text-align: center;
        }

        .close-btn {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: none;
            border: 2px solid var(--rdr-parchment);
            color: var(--rdr-parchment);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: var(--rdr-parchment);
            color: var(--rdr-border);
            transform: rotate(90deg);
        }

        /* Recent Dispatches Section */
        .recent-dispatches {
            background: rgba(78, 90, 62, 0.1);
            border: 2px dashed var(--rdr-border);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .recent-dispatches h3 {
            font-family: 'Cinzel', serif;
            color: var(--rdr-border);
            margin: 0 0 0.5rem 0;
            font-size: 1.1rem;
        }

        .dispatch-tape {
            background: var(--rdr-highlight);
            border: 1px solid var(--rdr-border);
            border-radius: 4px;
            padding: 0.5rem;
            margin: 0.25rem 0;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .dispatch-tape::before {
            content: '📡';
            margin-right: 0.5rem;
        }

        .dispatch-tape:hover {
            background: var(--rdr-border);
            color: var(--rdr-parchment);
            transform: translateX(5px);
        }

        .news-content {
            padding: 2rem;
        }

        .news-article {
            background: rgba(255, 255, 255, 0.4);
            border: 2px solid var(--rdr-border);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            position: relative;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transform-origin: center;
            transition: transform 0.3s ease;
        }

        .news-article:hover {
            transform: translateY(-2px);
        }

        .news-article::before {
            content: '📰';
            position: absolute;
            top: -10px;
            left: 20px;
            background: var(--rdr-parchment);
            padding: 5px 10px;
            border-radius: 50%;
            font-size: 1.2rem;
        }

        .article-title {
            font-family: 'Cinzel', serif;
            font-size: 1.2rem;
            color: var(--rdr-border);
            margin: 0 0 1rem 0;
            line-height: 1.4;
        }

        .article-content {
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 1rem;
            color: var(--rdr-text);
        }

        .article-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
            color: var(--rdr-border);
            border-top: 1px dashed var(--rdr-border);
            padding-top: 0.5rem;
        }

        .topic-tags {
            margin-top: 1rem;
        }

        .topic-tag {
            background: var(--rdr-accent);
            color: var(--rdr-parchment);
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            margin: 0.25rem;
            display: inline-block;
            border: 1px solid var(--rdr-border);
        }

        /* Enhanced Loading States */
        .loading-telegraph {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            flex-direction: column;
        }

        .telegraph-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid var(--rdr-parchment);
            border-top: 4px solid var(--rdr-border);
            border-radius: 50%;
            animation: telegraph-spin 1s linear infinite;
        }

        @keyframes telegraph-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Enhanced Controls Panel */
        .controls-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(216, 199, 160, 0.95);
            border: 2px solid var(--rdr-border);
            border-radius: 10px;
            padding: 1rem;
            backdrop-filter: blur(5px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .control-btn {
            background: var(--rdr-border);
            color: var(--rdr-parchment);
            border: none;
            padding: 0.5rem 1rem;
            margin: 0.25rem;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'IM Fell English', serif;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .control-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .control-btn:hover::before {
            left: 100%;
        }

        .control-btn:hover {
            background: var(--rdr-highlight);
            color: var(--rdr-text);
            transform: translateY(-2px);
        }

        .control-btn.active {
            background: var(--rdr-accent);
        }

        /* Accessibility Enhancements */
        .control-btn:focus,
        .telegraph-toggle:focus,
        .close-btn:focus {
            outline: 2px solid var(--rdr-highlight);
            outline-offset: 2px;
        }

        /* Enhanced Responsive Design */
        @media (max-width: 768px) {
            .telegraph-sidebar {
                width: 100vw;
                right: -100vw;
            }

            .telegraph-toggle.active {
                right: 100vw;
            }

            .controls-panel {
                position: relative;
                margin: 1rem;
                text-align: center;
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
            }

            .world-map-container {
                height: 50vh;
                margin: 1rem;
                min-height: 400px;
            }

            .frontier-header {
                padding: 1rem 0.5rem;
            }

            .news-content {
                padding: 1rem;
            }
        }

        @media (max-width: 480px) {
            .world-map-container {
                height: 40vh;
                min-height: 300px;
            }

            .controls-panel {
                padding: 0.5rem;
            }

            .control-btn {
                padding: 0.4rem 0.8rem;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body class="min-h-screen p-4">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        // Simple country data
        const COUNTRIES = {
            'United States': { searchTerm: 'United States news' },
            'Canada': { searchTerm: 'Canada news' },
            'Mexico': { searchTerm: 'Mexico news' },
            'Brazil': { searchTerm: 'Brazil news' },
            'Argentina': { searchTerm: 'Argentina news' },
            'United Kingdom': { searchTerm: 'United Kingdom news' },
            'France': { searchTerm: 'France news' },
            'Germany': { searchTerm: 'Germany news' },
            'Italy': { searchTerm: 'Italy news' },
            'Spain': { searchTerm: 'Spain news' },
            'Russia': { searchTerm: 'Russia news' },
            'China': { searchTerm: 'China news' },
            'India': { searchTerm: 'India news' },
            'Japan': { searchTerm: 'Japan news' },
            'Australia': { searchTerm: 'Australia news' },
            'South Africa': { searchTerm: 'South Africa news' },
            'Egypt': { searchTerm: 'Egypt news' },
            'Nigeria': { searchTerm: 'Nigeria news' }
        };

        // Simple sketched world map SVG
        const WorldMapSVG = ({ onCountryClick }) => (
            <svg className="world-map" viewBox="0 0 1000 500">
                {/* North America */}
                <path className="country" d="M150,100 Q200,80 250,100 L280,150 Q270,200 240,220 L180,200 Q140,150 150,100 Z"
                      onClick={() => onCountryClick('United States')} />
                <path className="country" d="M150,80 Q180,60 220,70 L240,90 Q200,80 150,80 Z"
                      onClick={() => onCountryClick('Canada')} />
                <path className="country" d="M180,220 Q220,240 250,230 L270,260 Q230,270 180,250 Z"
                      onClick={() => onCountryClick('Mexico')} />

                {/* South America */}
                <path className="country" d="M220,280 Q260,300 280,350 L270,400 Q240,420 210,400 L200,350 Q210,320 220,280 Z"
                      onClick={() => onCountryClick('Brazil')} />
                <path className="country" d="M200,400 Q230,420 250,450 L220,470 Q190,450 200,400 Z"
                      onClick={() => onCountryClick('Argentina')} />

                {/* Europe */}
                <path className="country" d="M450,120 Q470,110 490,120 L500,140 Q480,150 450,140 Z"
                      onClick={() => onCountryClick('United Kingdom')} />
                <path className="country" d="M480,140 Q520,130 540,150 L530,180 Q500,170 480,140 Z"
                      onClick={() => onCountryClick('France')} />
                <path className="country" d="M540,130 Q580,120 600,140 L590,170 Q560,160 540,130 Z"
                      onClick={() => onCountryClick('Germany')} />
                <path className="country" d="M520,180 Q560,170 580,190 L570,220 Q540,210 520,180 Z"
                      onClick={() => onCountryClick('Italy')} />
                <path className="country" d="M460,180 Q500,170 520,190 L510,220 Q480,210 460,180 Z"
                      onClick={() => onCountryClick('Spain')} />

                {/* Asia */}
                <path className="country" d="M600,100 Q700,80 800,120 L790,200 Q700,180 600,160 Z"
                      onClick={() => onCountryClick('Russia')} />
                <path className="country" d="M700,200 Q780,180 820,220 L800,280 Q720,260 700,200 Z"
                      onClick={() => onCountryClick('China')} />
                <path className="country" d="M650,280 Q720,260 750,300 L730,340 Q670,320 650,280 Z"
                      onClick={() => onCountryClick('India')} />
                <path className="country" d="M820,200 Q860,180 880,220 L870,260 Q840,240 820,200 Z"
                      onClick={() => onCountryClick('Japan')} />

                {/* Africa */}
                <path className="country" d="M480,240 Q540,220 580,260 L570,340 Q530,360 480,340 L470,280 Q475,260 480,240 Z"
                      onClick={() => onCountryClick('Egypt')} />
                <path className="country" d="M470,340 Q530,320 570,360 L560,420 Q520,440 470,420 Z"
                      onClick={() => onCountryClick('Nigeria')} />
                <path className="country" d="M520,420 Q570,400 600,440 L580,480 Q540,460 520,420 Z"
                      onClick={() => onCountryClick('South Africa')} />

                {/* Australia */}
                <path className="country" d="M750,380 Q820,360 860,400 L840,440 Q780,420 750,380 Z"
                      onClick={() => onCountryClick('Australia')} />
            </svg>
        );
        
        // Simple news search function
        const searchNews = async (searchTerm) => {
            try {
                // Use a simple search API or scraping approach
                const searchUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(`https://www.google.com/search?q=${encodeURIComponent(searchTerm)}&tbm=nws`)}`;
                const response = await fetch(searchUrl);
                const data = await response.json();

                // Parse the HTML response
                const parser = new DOMParser();
                const doc = parser.parseFromString(data.contents, 'text/html');

                const newsItems = [];
                const articles = doc.querySelectorAll('div[data-ved]');

                articles.forEach((article, index) => {
                    if (index >= 10) return; // Limit to 10 articles

                    const titleEl = article.querySelector('h3');
                    const snippetEl = article.querySelector('span');
                    const linkEl = article.querySelector('a');
                    const imgEl = article.querySelector('img');

                    if (titleEl && snippetEl) {
                        newsItems.push({
                            title: titleEl.textContent,
                            snippet: snippetEl.textContent,
                            url: linkEl ? linkEl.href : '#',
                            image: imgEl ? imgEl.src : null
                        });
                    }
                });

                return newsItems;
            } catch (error) {
                console.error('News search failed:', error);
                // Return mock data as fallback
                return generateMockNews(searchTerm);
            }
        };

        // Generate mock news data
        const generateMockNews = (searchTerm) => {
            const country = searchTerm.replace(' news', '');
            return [
                {
                    title: `Breaking: Major developments in ${country}`,
                    snippet: `Latest political and economic updates from ${country} as reported by international correspondents.`,
                    url: '#',
                    image: `https://picsum.photos/300/200?random=${Math.random()}`
                },
                {
                    title: `${country} economy shows new trends`,
                    snippet: `Economic analysts report significant changes in ${country}'s market conditions this week.`,
                    url: '#',
                    image: `https://picsum.photos/300/200?random=${Math.random()}`
                },
                {
                    title: `Cultural events highlight ${country}'s heritage`,
                    snippet: `Traditional celebrations and modern cultural movements gain attention in ${country}.`,
                    url: '#',
                    image: `https://picsum.photos/300/200?random=${Math.random()}`
                },
                {
                    title: `Technology sector advances in ${country}`,
                    snippet: `Innovation and technological development continue to shape ${country}'s future.`,
                    url: '#',
                    image: `https://picsum.photos/300/200?random=${Math.random()}`
                },
                {
                    title: `Environmental initiatives in ${country}`,
                    snippet: `New environmental policies and conservation efforts are being implemented across ${country}.`,
                    url: '#',
                    image: `https://picsum.photos/300/200?random=${Math.random()}`
                }
            ];
        };

        // Photo Frame Component
        const PhotoFrame = ({ image, title, snippet, onClose, style }) => (
            <div className="photo-frame" style={style} onClick={() => onClose()}>
                <button className="close-btn" onClick={(e) => { e.stopPropagation(); onClose(); }}>
                    ✕
                </button>
                <img src={image} alt={title} />
                <div className="photo-description">
                    <h4>{title}</h4>
                    <p>{snippet}</p>
                </div>
            </div>
        );

        // Main App Component
        const WesternNewsMap = () => {
            const [selectedCountry, setSelectedCountry] = useState(null);
            const [news, setNews] = useState([]);
            const [loading, setLoading] = useState(false);
            const [photos, setPhotos] = useState([]);

            const handleCountryClick = async (countryName) => {
                setSelectedCountry(countryName);
                setLoading(true);
                setNews([]);
                setPhotos([]);

                try {
                    const searchTerm = COUNTRIES[countryName]?.searchTerm || `${countryName} news`;
                    const newsData = await searchNews(searchTerm);
                    setNews(newsData);

                    // Display photos with random positions
                    const photosWithImages = newsData.filter(item => item.image);
                    const photoElements = photosWithImages.map((item, index) => ({
                        ...item,
                        id: index,
                        style: {
                            top: `${Math.random() * 60 + 10}%`,
                            left: `${Math.random() * 60 + 10}%`,
                            animationDelay: `${index * 0.5}s`
                        }
                    }));

                    setPhotos(photoElements);
                } catch (error) {
                    console.error('Error fetching news:', error);
                } finally {
                    setLoading(false);
                }
            };

            const handleNewsClick = (newsItem) => {
                if (newsItem.image) {
                    const newPhoto = {
                        ...newsItem,
                        id: Date.now(),
                        style: {
                            top: `${Math.random() * 60 + 10}%`,
                            left: `${Math.random() * 60 + 10}%`,
                        }
                    };
                    setPhotos(prev => [...prev, newPhoto]);
                }
            };

            const removePhoto = (photoId) => {
                setPhotos(prev => prev.filter(photo => photo.id !== photoId));
            };

            return (
                <div>
                    <h1 className="title">🤠 Western News Frontier 🤠</h1>

                    <div className="map-container">
                        <WorldMapSVG onCountryClick={handleCountryClick} />

                        {selectedCountry && (
                            <div style={{ marginTop: '1rem', textAlign: 'center' }}>
                                <h2>News from {selectedCountry}</h2>
                                {loading && <div className="loading">Searching the frontier for news...</div>}
                            </div>
                        )}

                        {news.length > 0 && (
                            <div className="news-container">
                                {news.map((item, index) => (
                                    <div
                                        key={index}
                                        className="news-item"
                                        onClick={() => handleNewsClick(item)}
                                    >
                                        <div className="news-title">{item.title}</div>
                                        <div className="news-snippet">{item.snippet}</div>
                                        {item.image && <div style={{ fontSize: '0.8rem', color: '#666', marginTop: '0.5rem' }}>📸 Click to view image</div>}
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>

                    {/* Photo Frames */}
                    {photos.map(photo => (
                        <PhotoFrame
                            key={photo.id}
                            image={photo.image}
                            title={photo.title}
                            snippet={photo.snippet}
                            style={photo.style}
                            onClose={() => removePhoto(photo.id)}
                        />
                    ))}
                </div>
            );
        };
        
        // Render the application
        ReactDOM.render(<WesternNewsMap />, document.getElementById('root'));
    </script>
</body>
</html>
