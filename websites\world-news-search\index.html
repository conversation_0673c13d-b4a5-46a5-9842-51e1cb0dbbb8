<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontier Telegraph - Enhanced World News Map</title>

    <!-- Font Preloading for Performance -->
    <link rel="preload" href="https://fonts.gstatic.com/s/imfellenglish/v16/Ktk6ALSWZJTd_ayvGFWZhbmEMw.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="https://fonts.gstatic.com/s/cinzel/v23/8vIJ7wMr0my-WxlCxLjy.woff2" as="font" type="font/woff2" crossorigin>

    <!-- Optimized Google Fonts - Reduced to Primary Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=IM+Fell+English:ital,wght@0,400;1,400&family=Cinzel:wght@400;600&display=swap" rel="stylesheet">

    <!-- Leaflet.js for Enhanced Map Performance -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

    <!-- Leaflet Plugins -->
    <script src="https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.js"></script>
    <script src="https://unpkg.com/leaflet-topojson@0.4.0/dist/leaflet-topojson.js"></script>

    <!-- React and ReactDOM -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>

    <!-- Babel for JSX -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

    <!-- Compromise.js for Enhanced Topic Extraction -->
    <script src="https://unpkg.com/compromise@14.10.0/builds/compromise.min.js"></script>

    <!-- Axios for HTTP Requests -->
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    
    <style>
        /* EXACT RDR2 COLOR SCHEME */
        :root {
            --rdr-parchment: #D8C7A0;
            --rdr-border: #3A2E24;
            --rdr-accent: #4E5A3E;
            --rdr-highlight: #d4a373;
            --rdr-text: #2D1810;
            --rdr-shadow: rgba(58, 46, 36, 0.8);
        }

        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'IM Fell English', serif;
            background:
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="25" cy="25" r="1" fill="%23D8C7A0" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="%23D8C7A0" opacity="0.1"/></svg>'),
                linear-gradient(135deg, var(--rdr-parchment) 0%, #C8B790 100%);
            color: var(--rdr-text);
            min-height: 100vh;
            overflow-x: hidden;
            background-size: 50px 50px;
        }

        /* Parchment Texture Enhancement */
        .parchment-texture {
            position: relative;
            filter: sepia(10%);
        }

        .parchment-texture::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"><defs><filter id="paper"><feTurbulence baseFrequency="0.04" numOctaves="5" result="noise"/><feDiffuseLighting in="noise" lighting-color="white" surfaceScale="1"><feDistantLight azimuth="45" elevation="60"/></feDiffuseLighting></filter></defs><rect width="100%" height="100%" filter="url(%23paper)" opacity="0.05"/></svg>');
            pointer-events: none;
            z-index: 1;
        }

        .parchment-texture {
            position: relative;
        }

        .parchment-texture::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(139, 69, 19, 0.05) 0%, transparent 2%),
                radial-gradient(circle at 75% 75%, rgba(139, 69, 19, 0.05) 0%, transparent 2%);
            background-size: 50px 50px;
            pointer-events: none;
            z-index: 1;
        }

        /* Enhanced Header with RDR2 Styling */
        .frontier-header {
            text-align: center;
            padding: 2rem 1rem;
            background: linear-gradient(180deg, rgba(58, 46, 36, 0.1) 0%, transparent 100%);
            border-bottom: 3px double var(--rdr-border);
            position: relative;
        }

        .frontier-title {
            font-family: 'Cinzel', serif;
            font-size: clamp(2rem, 5vw, 4rem);
            color: var(--rdr-border);
            text-shadow:
                2px 2px 0px var(--rdr-highlight),
                4px 4px 8px var(--rdr-shadow);
            margin: 0;
            letter-spacing: 2px;
            font-weight: 600;
        }

        .frontier-subtitle {
            font-family: 'IM Fell English', serif;
            font-size: clamp(1rem, 2.5vw, 1.5rem);
            color: var(--rdr-text);
            margin: 0.5rem 0 0 0;
            font-style: italic;
        }

        .telegraph-line {
            height: 2px;
            background: repeating-linear-gradient(
                90deg,
                var(--rdr-border) 0px,
                var(--rdr-border) 10px,
                transparent 10px,
                transparent 20px
            );
            margin: 1rem auto;
            width: 60%;
        }

        /* Enhanced Map Container for Leaflet */
        .world-map-container {
            position: relative;
            width: 100%;
            height: 70vh;
            min-height: 500px;
            background:
                radial-gradient(ellipse at center, rgba(212, 163, 115, 0.1) 0%, transparent 70%),
                linear-gradient(180deg, rgba(58, 46, 36, 0.05) 0%, rgba(78, 90, 62, 0.05) 100%);
            border: 4px solid var(--rdr-border);
            border-radius: 15px;
            margin: 2rem auto;
            max-width: 1200px;
            box-shadow:
                inset 0 0 50px rgba(58, 46, 36, 0.2),
                0 10px 30px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        /* Leaflet Map Styling */
        .leaflet-container {
            width: 100%;
            height: 100%;
            background: transparent;
            font-family: 'IM Fell English', serif;
        }

        .leaflet-control-zoom {
            border: 2px solid var(--rdr-border) !important;
            border-radius: 8px !important;
        }

        .leaflet-control-zoom a {
            background: var(--rdr-parchment) !important;
            color: var(--rdr-border) !important;
            border: none !important;
            font-family: 'IM Fell English', serif !important;
            font-weight: bold !important;
        }

        .leaflet-control-zoom a:hover {
            background: var(--rdr-highlight) !important;
        }

        /* Country Boundaries Styling */
        .leaflet-interactive {
            stroke: var(--rdr-border);
            stroke-width: 2;
            stroke-dasharray: 5,5;
            fill: rgba(216, 199, 160, 0.3);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .leaflet-interactive:hover {
            fill: rgba(212, 163, 115, 0.5);
            stroke-width: 3;
            stroke-dasharray: none;
            filter: drop-shadow(0 0 10px rgba(212, 163, 115, 0.8));
        }

        .news-marker {
            fill: var(--frontier-blood);
            stroke: var(--frontier-gold);
            stroke-width: 2;
            r: 4;
            opacity: 0.8;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .news-marker:hover {
            r: 8;
            opacity: 1;
            filter: drop-shadow(0 0 8px var(--frontier-blood));
        }

        .news-marker.pulsing {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { r: 4; opacity: 0.8; }
            50% { r: 6; opacity: 1; }
            100% { r: 4; opacity: 0.8; }
        }

        /* Enhanced Collapsible Telegraph Sidebar */
        .telegraph-sidebar {
            position: fixed;
            right: -420px;
            top: 0;
            width: 420px;
            height: 100vh;
            background:
                linear-gradient(135deg, var(--rdr-parchment) 0%, #C8B790 50%, #B8A780 100%);
            border-left: 4px solid var(--rdr-border);
            box-shadow: -10px 0 30px rgba(0, 0, 0, 0.4);
            transition: right 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            z-index: 1000;
            overflow-y: auto;
        }

        .telegraph-sidebar.open {
            right: 0;
        }

        /* Telegraph Key Toggle */
        .telegraph-toggle {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: var(--rdr-border);
            color: var(--rdr-parchment);
            border: none;
            width: 50px;
            height: 80px;
            border-radius: 25px 0 0 25px;
            cursor: pointer;
            font-size: 1.5rem;
            transition: all 0.3s ease;
            z-index: 999;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
        }

        .telegraph-toggle:hover {
            background: var(--rdr-highlight);
            transform: translateY(-50%) translateX(-5px);
        }

        .telegraph-toggle.active {
            right: 420px;
        }

        .sidebar-header {
            background: var(--rdr-border);
            color: var(--rdr-parchment);
            padding: 1.5rem;
            position: relative;
        }

        .sidebar-title {
            font-family: 'Cinzel', serif;
            font-size: 1.5rem;
            margin: 0;
            text-align: center;
        }

        .close-btn {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: none;
            border: 2px solid var(--rdr-parchment);
            color: var(--rdr-parchment);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: var(--rdr-parchment);
            color: var(--rdr-border);
            transform: rotate(90deg);
        }

        /* Recent Dispatches Section */
        .recent-dispatches {
            background: rgba(78, 90, 62, 0.1);
            border: 2px dashed var(--rdr-border);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .recent-dispatches h3 {
            font-family: 'Cinzel', serif;
            color: var(--rdr-border);
            margin: 0 0 0.5rem 0;
            font-size: 1.1rem;
        }

        .dispatch-tape {
            background: var(--rdr-highlight);
            border: 1px solid var(--rdr-border);
            border-radius: 4px;
            padding: 0.5rem;
            margin: 0.25rem 0;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .dispatch-tape::before {
            content: '📡';
            margin-right: 0.5rem;
        }

        .dispatch-tape:hover {
            background: var(--rdr-border);
            color: var(--rdr-parchment);
            transform: translateX(5px);
        }

        .news-content {
            padding: 2rem;
        }

        .news-article {
            background: rgba(255, 255, 255, 0.4);
            border: 2px solid var(--rdr-border);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            position: relative;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transform-origin: center;
            transition: transform 0.3s ease;
        }

        .news-article:hover {
            transform: translateY(-2px);
        }

        .news-article::before {
            content: '📰';
            position: absolute;
            top: -10px;
            left: 20px;
            background: var(--rdr-parchment);
            padding: 5px 10px;
            border-radius: 50%;
            font-size: 1.2rem;
        }

        .article-title {
            font-family: 'Cinzel', serif;
            font-size: 1.2rem;
            color: var(--rdr-border);
            margin: 0 0 1rem 0;
            line-height: 1.4;
        }

        .article-content {
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 1rem;
            color: var(--rdr-text);
        }

        .article-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
            color: var(--rdr-border);
            border-top: 1px dashed var(--rdr-border);
            padding-top: 0.5rem;
        }

        .topic-tags {
            margin-top: 1rem;
        }

        .topic-tag {
            background: var(--rdr-accent);
            color: var(--rdr-parchment);
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            margin: 0.25rem;
            display: inline-block;
            border: 1px solid var(--rdr-border);
        }

        /* Enhanced Loading States */
        .loading-telegraph {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            flex-direction: column;
        }

        .telegraph-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid var(--rdr-parchment);
            border-top: 4px solid var(--rdr-border);
            border-radius: 50%;
            animation: telegraph-spin 1s linear infinite;
        }

        @keyframes telegraph-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Enhanced Controls Panel */
        .controls-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(216, 199, 160, 0.95);
            border: 2px solid var(--rdr-border);
            border-radius: 10px;
            padding: 1rem;
            backdrop-filter: blur(5px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .control-btn {
            background: var(--rdr-border);
            color: var(--rdr-parchment);
            border: none;
            padding: 0.5rem 1rem;
            margin: 0.25rem;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'IM Fell English', serif;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .control-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .control-btn:hover::before {
            left: 100%;
        }

        .control-btn:hover {
            background: var(--rdr-highlight);
            color: var(--rdr-text);
            transform: translateY(-2px);
        }

        .control-btn.active {
            background: var(--rdr-accent);
        }

        /* Accessibility Enhancements */
        .control-btn:focus,
        .telegraph-toggle:focus,
        .close-btn:focus {
            outline: 2px solid var(--rdr-highlight);
            outline-offset: 2px;
        }

        /* Enhanced Responsive Design */
        @media (max-width: 768px) {
            .telegraph-sidebar {
                width: 100vw;
                right: -100vw;
            }

            .telegraph-toggle.active {
                right: 100vw;
            }

            .controls-panel {
                position: relative;
                margin: 1rem;
                text-align: center;
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
            }

            .world-map-container {
                height: 50vh;
                margin: 1rem;
                min-height: 400px;
            }

            .frontier-header {
                padding: 1rem 0.5rem;
            }

            .news-content {
                padding: 1rem;
            }
        }

        @media (max-width: 480px) {
            .world-map-container {
                height: 40vh;
                min-height: 300px;
            }

            .controls-panel {
                padding: 0.5rem;
            }

            .control-btn {
                padding: 0.4rem 0.8rem;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body class="min-h-screen p-4">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useRef, useMemo, useCallback } = React;

        // Enhanced News Configuration with NewsAPI Primary + Web Scraping Fallback
        const NEWS_API_KEY = 'your-newsapi-key-here'; // Replace with actual NewsAPI key
        const NEWS_API_BASE = 'https://newsapi.org/v2';
        const CORS_PROXY = 'https://api.allorigins.win/raw?url=';

        // Enhanced Country Data with NewsAPI Integration
        const WORLD_COUNTRIES = {
            'United States': {
                code: 'us',
                coords: [39.8283, -98.5795],
                newsApiCode: 'us',
                keywords: ['america', 'usa', 'united states', 'washington', 'biden', 'trump'],
                color: '#8B0000',
                fallbackSources: ['https://www.cnn.com', 'https://www.npr.org']
            },
            'United Kingdom': {
                code: 'gb',
                coords: [55.3781, -3.4360],
                newsApiCode: 'gb',
                keywords: ['britain', 'uk', 'england', 'london', 'sunak', 'brexit'],
                color: '#000080',
                fallbackSources: ['https://www.bbc.com/news', 'https://www.theguardian.com']
            },
            'France': {
                code: 'fr',
                coords: [46.2276, 2.2137],
                newsApiCode: 'fr',
                keywords: ['france', 'paris', 'macron', 'french'],
                color: '#0000FF',
                fallbackSources: ['https://www.france24.com/en/']
            },
            'Germany': {
                code: 'de',
                coords: [51.1657, 10.4515],
                newsApiCode: 'de',
                keywords: ['germany', 'berlin', 'german', 'scholz'],
                color: '#FFD700',
                fallbackSources: ['https://www.dw.com/en/']
            },
            'China': {
                code: 'cn',
                coords: [35.8617, 104.1954],
                newsApiCode: 'cn',
                keywords: ['china', 'beijing', 'chinese', 'xi jinping'],
                color: '#FF0000',
                fallbackSources: ['https://www.scmp.com']
            },
            'India': {
                code: 'in',
                coords: [20.5937, 78.9629],
                newsApiCode: 'in',
                keywords: ['india', 'delhi', 'mumbai', 'modi', 'indian'],
                color: '#FF8C00',
                fallbackSources: ['https://timesofindia.indiatimes.com']
            },
            'Japan': {
                code: 'jp',
                coords: [36.2048, 138.2529],
                newsApiCode: 'jp',
                keywords: ['japan', 'tokyo', 'japanese', 'kishida'],
                color: '#DC143C',
                fallbackSources: ['https://www.japantimes.co.jp']
            },
            'Australia': {
                code: 'au',
                coords: [-25.2744, 133.7751],
                newsApiCode: 'au',
                keywords: ['australia', 'sydney', 'melbourne', 'australian'],
                color: '#228B22',
                fallbackSources: ['https://www.smh.com.au']
            },
            'Russia': {
                code: 'ru',
                coords: [61.5240, 105.3188],
                newsApiCode: 'ru',
                keywords: ['russia', 'moscow', 'putin', 'russian', 'ukraine'],
                color: '#8B0000',
                fallbackSources: ['https://www.rt.com']
            },
            'Brazil': {
                code: 'br',
                coords: [-14.2350, -51.9253],
                newsApiCode: 'br',
                keywords: ['brazil', 'brasilia', 'sao paulo', 'lula', 'brazilian'],
                color: '#32CD32',
                fallbackSources: ['https://www.reuters.com']
            }
        };
        
        // Enhanced News Engine with NewsAPI Primary + Web Scraping Fallback
        class EnhancedNewsEngine {
            constructor() {
                this.cache = new Map();
                this.requestCount = 0;
                this.lastRequestTime = 0;
                this.retryDelays = [1000, 2000, 4000, 8000]; // Exponential backoff
            }

            // Versioned localStorage caching
            getCacheKey(countryCode, version = 'v1') {
                return `news_${countryCode}_${version}`;
            }

            getFromCache(countryCode) {
                const cacheKey = this.getCacheKey(countryCode);
                try {
                    const cached = localStorage.getItem(cacheKey);
                    if (cached) {
                        const data = JSON.parse(cached);
                        if (Date.now() - data.timestamp < 300000) { // 5 minutes
                            return data.articles;
                        }
                    }
                } catch (error) {
                    console.warn('Cache read error:', error);
                }
                return null;
            }

            setCache(countryCode, articles) {
                const cacheKey = this.getCacheKey(countryCode);
                try {
                    localStorage.setItem(cacheKey, JSON.stringify({
                        articles,
                        timestamp: Date.now()
                    }));
                } catch (error) {
                    console.warn('Cache write error:', error);
                }
            }

            // Request throttling
            async throttleRequest() {
                const now = Date.now();
                const timeSinceLastRequest = now - this.lastRequestTime;
                if (timeSinceLastRequest < 1000) { // 1 second throttle
                    await new Promise(resolve => setTimeout(resolve, 1000 - timeSinceLastRequest));
                }
                this.lastRequestTime = Date.now();
            }

            // Enhanced topic extraction using Compromise.js
            extractTopics(articles) {
                if (!window.nlp) return ['general'];

                const allText = articles.map(a => `${a.title} ${a.description || ''}`).join(' ');
                const doc = window.nlp(allText);

                // Extract nouns and noun phrases
                const nouns = doc.nouns().out('array');
                const topics = doc.topics().out('array');

                // Combine and filter
                const allTopics = [...nouns, ...topics]
                    .filter(topic => topic.length > 3 && topic.length < 20)
                    .map(topic => topic.toLowerCase())
                    .filter((topic, index, arr) => arr.indexOf(topic) === index)
                    .slice(0, 8);

                return allTopics.length > 0 ? allTopics : ['general', 'news'];
            }

            // Primary NewsAPI fetch
            async fetchFromNewsAPI(countryCode, retryCount = 0) {
                if (!NEWS_API_KEY || NEWS_API_KEY === 'your-newsapi-key-here') {
                    throw new Error('NewsAPI key not configured');
                }

                await this.throttleRequest();

                try {
                    const url = `${NEWS_API_BASE}/top-headlines?country=${countryCode}&apiKey=${NEWS_API_KEY}&pageSize=20`;
                    const response = await fetch(url);

                    if (!response.ok) {
                        throw new Error(`NewsAPI error: ${response.status}`);
                    }

                    const data = await response.json();
                    return data.articles || [];
                } catch (error) {
                    if (retryCount < this.retryDelays.length) {
                        console.warn(`NewsAPI retry ${retryCount + 1}:`, error.message);
                        await new Promise(resolve => setTimeout(resolve, this.retryDelays[retryCount]));
                        return this.fetchFromNewsAPI(countryCode, retryCount + 1);
                    }
                    throw error;
                }
            }

            // Fallback web scraping
            async fallbackWebScraping(countryData) {
                const articles = [];

                for (const sourceUrl of countryData.fallbackSources.slice(0, 2)) { // Limit to 2 sources
                    try {
                        const proxyUrl = `${CORS_PROXY}${encodeURIComponent(sourceUrl)}`;
                        const response = await fetch(proxyUrl);
                        const html = await response.text();

                        const parser = new DOMParser();
                        const doc = parser.parseFromString(html, 'text/html');

                        const selectors = [
                            'h1', 'h2', 'h3',
                            '.headline', '.title', '.story-title',
                            '[data-testid="card-headline"]',
                            '.entry-title', '.post-title'
                        ];

                        selectors.forEach(selector => {
                            const elements = doc.querySelectorAll(selector);
                            elements.forEach(el => {
                                const text = el.textContent?.trim();
                                if (text && text.length > 20 && text.length < 200) {
                                    articles.push({
                                        title: text,
                                        description: 'Telegraph transmission intercepted from frontier correspondents.',
                                        url: sourceUrl,
                                        source: { name: new URL(sourceUrl).hostname },
                                        publishedAt: new Date().toISOString()
                                    });
                                }
                            });
                        });
                    } catch (error) {
                        console.warn(`Scraping failed for ${sourceUrl}:`, error);
                    }
                }

                return articles.slice(0, 10);
            }

            // Generate emergency fallback news
            generateEmergencyFallback(countryName) {
                const templates = [
                    `Breaking developments reported in ${countryName}`,
                    `Economic indicators show activity in ${countryName}`,
                    `Political updates emerge from ${countryName}`,
                    `Regional affairs continue to develop in ${countryName}`,
                    `International attention focuses on ${countryName}`,
                    `Social movements gain traction in ${countryName}`,
                    `Technology sector advances in ${countryName}`,
                    `Environmental concerns addressed in ${countryName}`
                ];

                return templates.map((template, index) => ({
                    title: template,
                    description: 'Telegraph transmission from frontier correspondents. Detailed reports pending.',
                    url: '#',
                    source: { name: 'Frontier Telegraph' },
                    publishedAt: new Date(Date.now() - (index * 3600000)).toISOString()
                }));
            }

            // Main news fetching method
            async getNewsForCountry(countryName) {
                const countryData = WORLD_COUNTRIES[countryName];
                if (!countryData) return { articles: [], topics: [] };

                // Check cache first
                const cached = this.getFromCache(countryData.newsApiCode);
                if (cached) {
                    return {
                        articles: cached,
                        topics: this.extractTopics(cached)
                    };
                }

                let articles = [];

                try {
                    // Try NewsAPI first
                    articles = await this.fetchFromNewsAPI(countryData.newsApiCode);
                    console.log(`NewsAPI success for ${countryName}: ${articles.length} articles`);
                } catch (error) {
                    console.warn(`NewsAPI failed for ${countryName}:`, error.message);

                    try {
                        // Fallback to web scraping
                        articles = await this.fallbackWebScraping(countryData);
                        console.log(`Web scraping fallback for ${countryName}: ${articles.length} articles`);
                    } catch (scrapeError) {
                        console.warn(`Web scraping failed for ${countryName}:`, scrapeError.message);

                        // Emergency fallback
                        articles = this.generateEmergencyFallback(countryName);
                        console.log(`Emergency fallback for ${countryName}`);
                    }
                }

                // Filter and process articles
                const processedArticles = articles
                    .filter(article => article && article.title)
                    .slice(0, 8)
                    .map(article => ({
                        ...article,
                        relevance: this.calculateRelevance(article.title, countryData.keywords)
                    }))
                    .sort((a, b) => b.relevance - a.relevance);

                // Cache the results
                this.setCache(countryData.newsApiCode, processedArticles);

                return {
                    articles: processedArticles,
                    topics: this.extractTopics(processedArticles)
                };
            }

            calculateRelevance(title, keywords) {
                const text = title.toLowerCase();
                let score = 0;

                // Country-specific keywords
                keywords.forEach(keyword => {
                    if (text.includes(keyword.toLowerCase())) score += 2;
                });

                // General importance keywords
                const importantWords = ['breaking', 'urgent', 'crisis', 'war', 'election', 'economy'];
                importantWords.forEach(word => {
                    if (text.includes(word)) score += 1;
                });

                return score;
            }
        }

        // Enhanced Telegraph Sidebar with Recent Dispatches
        const TelegraphSidebar = ({ isOpen, selectedCountry, news, topics, loading, onClose, recentDispatches, onDispatchClick }) => {
            const memoizedNews = useMemo(() => news, [news]);
            const memoizedTopics = useMemo(() => topics, [topics]);

            return (
                <div className={`telegraph-sidebar ${isOpen ? 'open' : ''}`}>
                    <div className="sidebar-header">
                        <h2 className="sidebar-title">
                            📡 Telegraph Report
                        </h2>
                        <button
                            className="close-btn"
                            onClick={onClose}
                            aria-label="Close telegraph report"
                        >
                            ✕
                        </button>
                        {selectedCountry && (
                            <div style={{ textAlign: 'center', marginTop: '1rem', fontSize: '1.1rem' }}>
                                {selectedCountry}
                            </div>
                        )}
                    </div>

                    <div className="news-content">
                        {/* Recent Dispatches Section */}
                        {recentDispatches.length > 0 && (
                            <div className="recent-dispatches">
                                <h3>📜 Recent Dispatches</h3>
                                {recentDispatches.map((dispatch, index) => (
                                    <div
                                        key={index}
                                        className="dispatch-tape"
                                        onClick={() => onDispatchClick(dispatch)}
                                        role="button"
                                        tabIndex={0}
                                        onKeyPress={(e) => e.key === 'Enter' && onDispatchClick(dispatch)}
                                    >
                                        {dispatch}
                                    </div>
                                ))}
                            </div>
                        )}

                        {loading ? (
                            <div className="loading-telegraph">
                                <div className="telegraph-spinner"></div>
                                <p style={{ marginTop: '1rem', textAlign: 'center' }}>
                                    Intercepting telegraph signals...
                                </p>
                            </div>
                        ) : (
                            <>
                                {/* Topics Section */}
                                {memoizedTopics.length > 0 && (
                                    <div className="topic-tags">
                                        <h4 style={{ marginBottom: '0.5rem', color: 'var(--rdr-border)' }}>
                                            📋 Key Topics
                                        </h4>
                                        {memoizedTopics.map((topic, index) => (
                                            <span key={index} className="topic-tag">
                                                {topic}
                                            </span>
                                        ))}
                                    </div>
                                )}

                                {/* News Articles */}
                                {memoizedNews.length === 0 ? (
                                    <div style={{ textAlign: 'center', padding: '2rem' }}>
                                        <p>No telegraph signals detected from this region.</p>
                                        <p style={{ fontSize: '0.9rem', marginTop: '1rem', fontStyle: 'italic' }}>
                                            Try checking your NewsAPI configuration or network connection.
                                        </p>
                                    </div>
                                ) : (
                                    memoizedNews.map((article, index) => (
                                        <div key={index} className="news-article">
                                            <h3 className="article-title">{article.title}</h3>
                                            <p className="article-content">
                                                {article.description || "Telegraph transmission intercepted from frontier correspondents."}
                                            </p>
                                            <div className="article-meta">
                                                <span>📰 {article.source?.name || 'Frontier Telegraph'}</span>
                                                <span>🕐 {new Date(article.publishedAt || Date.now()).toLocaleTimeString()}</span>
                                            </div>
                                            {article.url && article.url !== '#' && (
                                                <div style={{ marginTop: '0.5rem' }}>
                                                    <a
                                                        href={article.url}
                                                        target="_blank"
                                                        rel="noopener noreferrer"
                                                        style={{
                                                            color: 'var(--rdr-border)',
                                                            textDecoration: 'none',
                                                            fontSize: '0.9rem'
                                                        }}
                                                    >
                                                        📖 Read Full Report →
                                                    </a>
                                                </div>
                                            )}
                                        </div>
                                    ))
                                )}
                            </>
                        )}
                    </div>
                </div>
            );
        };
        
        // Enhanced Leaflet-based World Map Component
        const FrontierWorldMap = () => {
            const mapRef = useRef(null);
            const leafletMapRef = useRef(null);
            const heatmapLayerRef = useRef(null);
            const countryLayerRef = useRef(null);

            const [selectedCountry, setSelectedCountry] = useState(null);
            const [news, setNews] = useState([]);
            const [topics, setTopics] = useState([]);
            const [loading, setLoading] = useState(false);
            const [sidebarOpen, setSidebarOpen] = useState(false);
            const [showHeatmap, setShowHeatmap] = useState(false);
            const [recentDispatches, setRecentDispatches] = useState([]);

            const newsEngine = useRef(new EnhancedNewsEngine());
            
            // Initialize Leaflet map
            useEffect(() => {
                if (!mapRef.current || leafletMapRef.current) return;

                // Create Leaflet map with enhanced styling
                const map = L.map(mapRef.current, {
                    center: [20, 0],
                    zoom: 2,
                    minZoom: 2,
                    maxZoom: 6,
                    zoomControl: true,
                    worldCopyJump: true
                });

                leafletMapRef.current = map;

                // Add custom tile layer with RDR2 aesthetic
                L.tileLayer('https://stamen-tiles.a.ssl.fastly.net/watercolor/{z}/{x}/{y}.jpg', {
                    attribution: '&copy; <a href="http://stamen.com">Stamen Design</a>',
                    maxZoom: 6,
                    opacity: 0.7
                }).addTo(map);

                // Load and add country boundaries
                loadCountryBoundaries(map);

                // Add country markers
                addCountryMarkers(map);

                return () => {
                    if (leafletMapRef.current) {
                        leafletMapRef.current.remove();
                        leafletMapRef.current = null;
                    }
                };
            }, []);

            // Load country boundaries using TopoJSON
            const loadCountryBoundaries = async (map) => {
                try {
                    const response = await fetch('https://cdn.jsdelivr.net/npm/world-atlas@2/countries-110m.json');
                    const world = await response.json();

                    // Convert TopoJSON to GeoJSON
                    const countries = topojson.feature(world, world.objects.countries);

                    // Add country boundaries layer
                    countryLayerRef.current = L.geoJSON(countries, {
                        style: {
                            color: '#3A2E24',
                            weight: 2,
                            opacity: 0.8,
                            fillColor: '#D8C7A0',
                            fillOpacity: 0.3,
                            dashArray: '5,5'
                        },
                        onEachFeature: (feature, layer) => {
                            const countryName = getCountryNameFromFeature(feature);

                            layer.on({
                                mouseover: (e) => {
                                    const layer = e.target;
                                    layer.setStyle({
                                        weight: 3,
                                        fillOpacity: 0.5,
                                        fillColor: '#d4a373',
                                        dashArray: null
                                    });

                                    // Show popup
                                    layer.bindPopup(`
                                        <div style="font-family: 'IM Fell English', serif; text-align: center;">
                                            <strong>${countryName}</strong><br>
                                            <small>Click to intercept telegraph signals</small>
                                        </div>
                                    `).openPopup();
                                },
                                mouseout: (e) => {
                                    countryLayerRef.current.resetStyle(e.target);
                                    e.target.closePopup();
                                },
                                click: (e) => {
                                    if (WORLD_COUNTRIES[countryName]) {
                                        handleCountryClick(countryName);

                                        // Highlight selected country
                                        countryLayerRef.current.eachLayer(layer => {
                                            countryLayerRef.current.resetStyle(layer);
                                        });

                                        e.target.setStyle({
                                            color: '#8B0000',
                                            weight: 4,
                                            fillColor: '#8B0000',
                                            fillOpacity: 0.4,
                                            dashArray: null
                                        });
                                    }
                                }
                            });
                        }
                    }).addTo(map);

                } catch (error) {
                    console.error('Failed to load country boundaries:', error);
                    // Fallback to simple markers only
                }
            };

            // Add country markers with zoom-based labels
            const addCountryMarkers = (map) => {
                Object.entries(WORLD_COUNTRIES).forEach(([countryName, data]) => {
                    const [lat, lng] = data.coords;

                    // Create custom marker
                    const marker = L.circleMarker([lat, lng], {
                        radius: 8,
                        fillColor: data.color,
                        color: '#3A2E24',
                        weight: 2,
                        opacity: 1,
                        fillOpacity: 0.8
                    }).addTo(map);

                    // Add pulsing effect for activity
                    marker.on('mouseover', function() {
                        this.setRadius(12);
                        this.setStyle({ fillOpacity: 1 });
                    });

                    marker.on('mouseout', function() {
                        this.setRadius(8);
                        this.setStyle({ fillOpacity: 0.8 });
                    });

                    marker.on('click', () => {
                        handleCountryClick(countryName);
                    });

                    // Add zoom-based labels
                    map.on('zoomend', () => {
                        if (map.getZoom() >= 4) {
                            if (!marker.getTooltip()) {
                                marker.bindTooltip(countryName, {
                                    permanent: true,
                                    direction: 'top',
                                    className: 'country-label',
                                    offset: [0, -15]
                                });
                            }
                            marker.openTooltip();
                        } else {
                            if (marker.getTooltip()) {
                                marker.closeTooltip();
                                marker.unbindTooltip();
                            }
                        }
                    });
                });
            };

            // Helper functions
            const getCountryNameFromFeature = (feature) => {
                const props = feature.properties;
                const possibleNames = [props.NAME, props.NAME_EN, props.ADMIN, props.NAME_LONG];

                for (const name of possibleNames) {
                    if (name && WORLD_COUNTRIES[name]) {
                        return name;
                    }
                }

                // Fallback mapping for common mismatches
                const nameMapping = {
                    'United States of America': 'United States',
                    'Russian Federation': 'Russia',
                    'People\'s Republic of China': 'China'
                };

                for (const name of possibleNames) {
                    if (name && nameMapping[name]) {
                        return nameMapping[name];
                    }
                }

                return possibleNames[0] || 'Unknown';
            };

            // Enhanced country click handler
            const handleCountryClick = useCallback(async (countryName) => {
                setSelectedCountry(countryName);
                setLoading(true);
                setSidebarOpen(true);

                // Add to recent dispatches
                setRecentDispatches(prev => {
                    const updated = [countryName, ...prev.filter(c => c !== countryName)];
                    return updated.slice(0, 3); // Keep only last 3
                });

                try {
                    const newsData = await newsEngine.current.getNewsForCountry(countryName);
                    setNews(newsData.articles);
                    setTopics(newsData.topics);
                } catch (error) {
                    console.error('Error fetching news:', error);
                    setNews([]);
                    setTopics([]);
                } finally {
                    setLoading(false);
                }
            }, []);

            // Handle dispatch click
            const handleDispatchClick = useCallback((countryName) => {
                handleCountryClick(countryName);
            }, [handleCountryClick]);

            // Toggle heatmap
            const toggleHeatmap = useCallback(() => {
                if (!leafletMapRef.current) return;

                if (showHeatmap && heatmapLayerRef.current) {
                    leafletMapRef.current.removeLayer(heatmapLayerRef.current);
                    heatmapLayerRef.current = null;
                } else if (!showHeatmap) {
                    // Create heatmap data
                    const heatmapData = Object.entries(WORLD_COUNTRIES).map(([name, data]) => {
                        const [lat, lng] = data.coords;
                        return [lat, lng, Math.random() * 0.8 + 0.2]; // Random intensity
                    });

                    heatmapLayerRef.current = L.heatLayer(heatmapData, {
                        radius: 50,
                        blur: 35,
                        maxZoom: 6,
                        gradient: {
                            0.0: '#4E5A3E',
                            0.5: '#d4a373',
                            1.0: '#D8C7A0'
                        }
                    }).addTo(leafletMapRef.current);
                }

                setShowHeatmap(!showHeatmap);
            }, [showHeatmap]);

            // Close sidebar
            const closeSidebar = useCallback(() => {
                setSidebarOpen(false);
                setSelectedCountry(null);

                // Reset country highlighting
                if (countryLayerRef.current) {
                    countryLayerRef.current.eachLayer(layer => {
                        countryLayerRef.current.resetStyle(layer);
                    });
                }
            }, []);

            // Keyboard navigation
            useEffect(() => {
                const handleKeyPress = (e) => {
                    if (e.key === 'Escape' && sidebarOpen) {
                        closeSidebar();
                    }
                };

                document.addEventListener('keydown', handleKeyPress);
                return () => document.removeEventListener('keydown', handleKeyPress);
            }, [sidebarOpen, closeSidebar]);

            return (
                <div className="parchment-texture">
                    {/* Header */}
                    <header className="frontier-header">
                        <h1 className="frontier-title">
                            ⚡ FRONTIER TELEGRAPH ⚡
                        </h1>
                        <p className="frontier-subtitle">
                            Enhanced World News Intelligence Network
                        </p>
                        <div className="telegraph-line"></div>
                    </header>

                    {/* Telegraph Toggle Button */}
                    <button
                        className={`telegraph-toggle ${sidebarOpen ? 'active' : ''}`}
                        onClick={() => setSidebarOpen(!sidebarOpen)}
                        aria-label="Toggle telegraph reports"
                    >
                        📡
                    </button>

                    {/* Main Map Container */}
                    <div className="world-map-container">
                        {/* Enhanced Controls Panel */}
                        <div className="controls-panel">
                            <button
                                className={`control-btn ${showHeatmap ? 'active' : ''}`}
                                onClick={toggleHeatmap}
                                aria-label="Toggle news activity heatmap"
                            >
                                🔥 {showHeatmap ? 'Hide' : 'Show'} Activity Heatmap
                            </button>
                            <button
                                className="control-btn"
                                onClick={() => window.location.reload()}
                                aria-label="Refresh telegraph network"
                            >
                                🔄 Refresh Network
                            </button>
                            <button
                                className="control-btn"
                                onClick={() => {
                                    if (leafletMapRef.current) {
                                        leafletMapRef.current.setView([20, 0], 2);
                                    }
                                }}
                                aria-label="Reset map view"
                            >
                                🌍 Reset View
                            </button>
                        </div>

                        {/* Leaflet Map Container */}
                        <div ref={mapRef} style={{ width: '100%', height: '100%' }} />
                    </div>

                    {/* Enhanced Telegraph Sidebar */}
                    <TelegraphSidebar
                        isOpen={sidebarOpen}
                        selectedCountry={selectedCountry}
                        news={news}
                        topics={topics}
                        loading={loading}
                        recentDispatches={recentDispatches}
                        onClose={closeSidebar}
                        onDispatchClick={handleDispatchClick}
                    />

                    {/* Enhanced Instructions */}
                    <div style={{
                        textAlign: 'center',
                        padding: '2rem',
                        fontStyle: 'italic',
                        color: 'var(--rdr-border)',
                        maxWidth: '800px',
                        margin: '0 auto'
                    }}>
                        <p>🗺️ <strong>Click on any country</strong> to intercept telegraph reports from that region</p>
                        <p style={{ fontSize: '0.9rem', marginTop: '0.5rem' }}>
                            📡 Enhanced with NewsAPI integration and intelligent web scraping fallback
                        </p>
                        <p style={{ fontSize: '0.8rem', marginTop: '0.5rem', opacity: 0.8 }}>
                            🔧 Configure your NewsAPI key in the code for live news data
                        </p>
                    </div>
                </div>
            );
        };

        // Render the application
        ReactDOM.render(<FrontierWorldMap />, document.getElementById('root'));
    </script>
</body>
</html>
