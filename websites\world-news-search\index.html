<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontier News Telegraph - World Events Map</title>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=IM+Fell+English:ital,wght@0,400;1,400&family=Cinzel:wght@400;600&family=Uncial+Antiqua&display=swap" rel="stylesheet">

    <!-- D3.js for advanced map visualization -->
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="https://unpkg.com/topojson@3"></script>

    <!-- React and ReactDOM -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>

    <!-- Babel for JSX -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

    <!-- CORS Proxy for web scraping -->
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    
    <style>
        :root {
            --frontier-parchment: #F4E4BC;
            --frontier-leather: #8B4513;
            --frontier-ink: #2F1B14;
            --frontier-gold: #DAA520;
            --frontier-blood: #8B0000;
            --frontier-forest: #228B22;
            --frontier-shadow: rgba(47, 27, 20, 0.8);
        }

        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'IM Fell English', serif;
            background:
                radial-gradient(circle at 20% 80%, rgba(139, 69, 19, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(218, 165, 32, 0.1) 0%, transparent 50%),
                linear-gradient(135deg, #F4E4BC 0%, #E6D3A3 100%);
            color: var(--frontier-ink);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .parchment-texture {
            position: relative;
        }

        .parchment-texture::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(139, 69, 19, 0.05) 0%, transparent 2%),
                radial-gradient(circle at 75% 75%, rgba(139, 69, 19, 0.05) 0%, transparent 2%);
            background-size: 50px 50px;
            pointer-events: none;
            z-index: 1;
        }

        .frontier-header {
            text-align: center;
            padding: 2rem 1rem;
            background: linear-gradient(180deg, rgba(139, 69, 19, 0.1) 0%, transparent 100%);
            border-bottom: 3px double var(--frontier-leather);
            position: relative;
        }

        .frontier-title {
            font-family: 'Uncial Antiqua', cursive;
            font-size: clamp(2rem, 5vw, 4rem);
            color: var(--frontier-leather);
            text-shadow:
                2px 2px 0px var(--frontier-gold),
                4px 4px 8px var(--frontier-shadow);
            margin: 0;
            letter-spacing: 2px;
        }

        .frontier-subtitle {
            font-family: 'Cinzel', serif;
            font-size: clamp(1rem, 2.5vw, 1.5rem);
            color: var(--frontier-ink);
            margin: 0.5rem 0 0 0;
            font-style: italic;
        }

        .telegraph-line {
            height: 2px;
            background: repeating-linear-gradient(
                90deg,
                var(--frontier-leather) 0px,
                var(--frontier-leather) 10px,
                transparent 10px,
                transparent 20px
            );
            margin: 1rem auto;
            width: 60%;
        }

        .world-map-container {
            position: relative;
            width: 100%;
            height: 70vh;
            min-height: 500px;
            background:
                radial-gradient(ellipse at center, rgba(218, 165, 32, 0.1) 0%, transparent 70%),
                linear-gradient(180deg, rgba(139, 69, 19, 0.05) 0%, rgba(34, 139, 34, 0.05) 100%);
            border: 4px solid var(--frontier-leather);
            border-radius: 15px;
            margin: 2rem auto;
            max-width: 1200px;
            box-shadow:
                inset 0 0 50px rgba(139, 69, 19, 0.2),
                0 10px 30px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        .map-svg {
            width: 100%;
            height: 100%;
            cursor: crosshair;
        }

        .country-path {
            stroke: var(--frontier-leather);
            stroke-width: 1.5;
            stroke-dasharray: 3,2;
            fill: rgba(244, 228, 188, 0.3);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .country-path:hover {
            fill: rgba(218, 165, 32, 0.4);
            stroke-width: 2.5;
            stroke-dasharray: none;
            filter: drop-shadow(0 0 10px rgba(218, 165, 32, 0.6));
        }

        .country-path.active {
            fill: rgba(139, 0, 0, 0.3);
            stroke: var(--frontier-blood);
            stroke-width: 3;
            stroke-dasharray: none;
        }

        .news-marker {
            fill: var(--frontier-blood);
            stroke: var(--frontier-gold);
            stroke-width: 2;
            r: 4;
            opacity: 0.8;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .news-marker:hover {
            r: 8;
            opacity: 1;
            filter: drop-shadow(0 0 8px var(--frontier-blood));
        }

        .news-marker.pulsing {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { r: 4; opacity: 0.8; }
            50% { r: 6; opacity: 1; }
            100% { r: 4; opacity: 0.8; }
        }

        .telegraph-sidebar {
            position: fixed;
            right: -400px;
            top: 0;
            width: 400px;
            height: 100vh;
            background:
                linear-gradient(135deg, #F4E4BC 0%, #E6D3A3 50%, #D4C294 100%);
            border-left: 4px solid var(--frontier-leather);
            box-shadow: -10px 0 30px rgba(0, 0, 0, 0.4);
            transition: right 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            z-index: 1000;
            overflow-y: auto;
        }

        .telegraph-sidebar.open {
            right: 0;
        }

        .sidebar-header {
            background: var(--frontier-leather);
            color: var(--frontier-parchment);
            padding: 1.5rem;
            position: relative;
        }

        .sidebar-title {
            font-family: 'Cinzel', serif;
            font-size: 1.5rem;
            margin: 0;
            text-align: center;
        }

        .close-btn {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: none;
            border: 2px solid var(--frontier-parchment);
            color: var(--frontier-parchment);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: var(--frontier-parchment);
            color: var(--frontier-leather);
            transform: rotate(90deg);
        }

        .news-content {
            padding: 2rem;
        }

        .news-article {
            background: rgba(255, 255, 255, 0.3);
            border: 2px solid var(--frontier-leather);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            position: relative;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .news-article::before {
            content: '📰';
            position: absolute;
            top: -10px;
            left: 20px;
            background: var(--frontier-parchment);
            padding: 5px 10px;
            border-radius: 50%;
            font-size: 1.2rem;
        }

        .article-title {
            font-family: 'Cinzel', serif;
            font-size: 1.2rem;
            color: var(--frontier-leather);
            margin: 0 0 1rem 0;
            line-height: 1.4;
        }

        .article-content {
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .article-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
            color: var(--frontier-leather);
            border-top: 1px dashed var(--frontier-leather);
            padding-top: 0.5rem;
        }

        .loading-telegraph {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            flex-direction: column;
        }

        .telegraph-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid var(--frontier-parchment);
            border-top: 4px solid var(--frontier-leather);
            border-radius: 50%;
            animation: telegraph-spin 1s linear infinite;
        }

        @keyframes telegraph-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .controls-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(244, 228, 188, 0.9);
            border: 2px solid var(--frontier-leather);
            border-radius: 10px;
            padding: 1rem;
            backdrop-filter: blur(5px);
        }

        .control-btn {
            background: var(--frontier-leather);
            color: var(--frontier-parchment);
            border: none;
            padding: 0.5rem 1rem;
            margin: 0.25rem;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'IM Fell English', serif;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: var(--frontier-gold);
            color: var(--frontier-ink);
            transform: translateY(-2px);
        }

        .control-btn.active {
            background: var(--frontier-blood);
        }

        @media (max-width: 768px) {
            .telegraph-sidebar {
                width: 100vw;
                right: -100vw;
            }

            .controls-panel {
                position: relative;
                margin: 1rem;
                text-align: center;
            }

            .world-map-container {
                height: 50vh;
                margin: 1rem;
            }
        }
    </style>
</head>
<body class="min-h-screen p-4">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useRef, useMemo } = React;

        // Advanced web scraping configuration
        const CORS_PROXY = 'https://api.allorigins.win/raw?url=';
        const NEWS_SOURCES = {
            'BBC': 'https://www.bbc.com/news',
            'CNN': 'https://edition.cnn.com',
            'Reuters': 'https://www.reuters.com',
            'Guardian': 'https://www.theguardian.com/international',
            'AlJazeera': 'https://www.aljazeera.com',
            'NPR': 'https://www.npr.org/sections/news/',
            'Associated Press': 'https://apnews.com',
            'France24': 'https://www.france24.com/en/',
            'Deutsche Welle': 'https://www.dw.com/en/',
            'RT': 'https://www.rt.com',
            'SCMP': 'https://www.scmp.com',
            'Times of India': 'https://timesofindia.indiatimes.com',
            'Japan Times': 'https://www.japantimes.co.jp',
            'Sydney Morning Herald': 'https://www.smh.com.au'
        };

        // Country mapping with enhanced data
        const WORLD_COUNTRIES = {
            'United States': {
                code: 'US',
                sources: ['CNN', 'NPR', 'Associated Press'],
                keywords: ['america', 'usa', 'united states', 'washington', 'biden', 'trump'],
                color: '#8B0000'
            },
            'United Kingdom': {
                code: 'GB',
                sources: ['BBC', 'Guardian'],
                keywords: ['britain', 'uk', 'england', 'london', 'sunak', 'brexit'],
                color: '#000080'
            },
            'France': {
                code: 'FR',
                sources: ['France24'],
                keywords: ['france', 'paris', 'macron', 'french'],
                color: '#0000FF'
            },
            'Germany': {
                code: 'DE',
                sources: ['Deutsche Welle'],
                keywords: ['germany', 'berlin', 'german', 'scholz'],
                color: '#FFD700'
            },
            'China': {
                code: 'CN',
                sources: ['SCMP'],
                keywords: ['china', 'beijing', 'chinese', 'xi jinping'],
                color: '#FF0000'
            },
            'India': {
                code: 'IN',
                sources: ['Times of India'],
                keywords: ['india', 'delhi', 'mumbai', 'modi', 'indian'],
                color: '#FF8C00'
            },
            'Japan': {
                code: 'JP',
                sources: ['Japan Times'],
                keywords: ['japan', 'tokyo', 'japanese', 'kishida'],
                color: '#DC143C'
            },
            'Australia': {
                code: 'AU',
                sources: ['Sydney Morning Herald'],
                keywords: ['australia', 'sydney', 'melbourne', 'australian'],
                color: '#228B22'
            },
            'Russia': {
                code: 'RU',
                sources: ['RT'],
                keywords: ['russia', 'moscow', 'putin', 'russian', 'ukraine'],
                color: '#8B0000'
            },
            'Brazil': {
                code: 'BR',
                sources: ['Reuters'],
                keywords: ['brazil', 'brasilia', 'sao paulo', 'lula', 'brazilian'],
                color: '#32CD32'
            }
        };
        
        // Advanced News Scraper
        class NewsScrapingEngine {
            constructor() {
                this.cache = new Map();
                this.isScrapingActive = false;
            }

            async scrapeNewsFromSource(source, url) {
                try {
                    const proxyUrl = `${CORS_PROXY}${encodeURIComponent(url)}`;
                    const response = await fetch(proxyUrl);
                    const html = await response.text();

                    // Create a temporary DOM parser
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');

                    // Extract headlines using multiple selectors
                    const selectors = [
                        'h1', 'h2', 'h3',
                        '.headline', '.title', '.story-title',
                        '[data-testid="card-headline"]',
                        '.entry-title', '.post-title',
                        'article h1', 'article h2'
                    ];

                    const headlines = [];
                    selectors.forEach(selector => {
                        const elements = doc.querySelectorAll(selector);
                        elements.forEach(el => {
                            const text = el.textContent?.trim();
                            if (text && text.length > 20 && text.length < 200) {
                                headlines.push({
                                    title: text,
                                    source: source,
                                    url: url,
                                    timestamp: Date.now(),
                                    relevance: this.calculateRelevance(text)
                                });
                            }
                        });
                    });

                    return headlines.slice(0, 10); // Limit to top 10
                } catch (error) {
                    console.warn(`Failed to scrape ${source}:`, error);
                    return this.generateFallbackNews(source);
                }
            }

            calculateRelevance(text) {
                const keywords = ['breaking', 'urgent', 'crisis', 'war', 'election', 'economy', 'climate'];
                let score = 0;
                keywords.forEach(keyword => {
                    if (text.toLowerCase().includes(keyword)) score += 1;
                });
                return score;
            }

            generateFallbackNews(source) {
                const templates = [
                    "Political tensions rise in regional disputes",
                    "Economic indicators show mixed signals",
                    "Climate change impacts continue to mount",
                    "Technology sector faces new regulations",
                    "International trade negotiations ongoing",
                    "Healthcare systems adapt to new challenges",
                    "Energy markets experience volatility",
                    "Social movements gain momentum globally"
                ];

                return templates.map((template, index) => ({
                    title: `${template} - ${source} Report`,
                    source: source,
                    url: '#',
                    timestamp: Date.now() - (index * 3600000),
                    relevance: Math.random() * 3,
                    description: `Latest developments from ${source} covering regional and international affairs.`
                }));
            }

            async getNewsForCountry(countryName) {
                if (this.cache.has(countryName)) {
                    const cached = this.cache.get(countryName);
                    if (Date.now() - cached.timestamp < 300000) { // 5 minutes cache
                        return cached.data;
                    }
                }

                const countryData = WORLD_COUNTRIES[countryName];
                if (!countryData) return [];

                const allNews = [];

                // Scrape from multiple sources
                for (const sourceName of countryData.sources) {
                    const sourceUrl = NEWS_SOURCES[sourceName];
                    if (sourceUrl) {
                        const news = await this.scrapeNewsFromSource(sourceName, sourceUrl);
                        allNews.push(...news);
                    }
                }

                // Filter and rank news by country relevance
                const relevantNews = allNews.filter(article => {
                    const text = article.title.toLowerCase();
                    return countryData.keywords.some(keyword => text.includes(keyword));
                }).sort((a, b) => b.relevance - a.relevance);

                // Add some general news if country-specific news is limited
                if (relevantNews.length < 5) {
                    relevantNews.push(...allNews.slice(0, 8 - relevantNews.length));
                }

                const result = relevantNews.slice(0, 8);
                this.cache.set(countryName, { data: result, timestamp: Date.now() });

                return result;
            }
        }

        // Telegraph Sidebar Component
        const TelegraphSidebar = ({ isOpen, selectedCountry, news, loading, onClose }) => {
            return (
                <div className={`telegraph-sidebar ${isOpen ? 'open' : ''}`}>
                    <div className="sidebar-header">
                        <h2 className="sidebar-title">
                            📡 Telegraph Report
                        </h2>
                        <button className="close-btn" onClick={onClose}>
                            ✕
                        </button>
                        {selectedCountry && (
                            <div style={{ textAlign: 'center', marginTop: '1rem', fontSize: '1.1rem' }}>
                                {selectedCountry}
                            </div>
                        )}
                    </div>

                    <div className="news-content">
                        {loading ? (
                            <div className="loading-telegraph">
                                <div className="telegraph-spinner"></div>
                                <p style={{ marginTop: '1rem', textAlign: 'center' }}>
                                    Intercepting telegraph signals...
                                </p>
                            </div>
                        ) : (
                            <>
                                {news.length === 0 ? (
                                    <div style={{ textAlign: 'center', padding: '2rem' }}>
                                        <p>No telegraph signals detected from this region.</p>
                                    </div>
                                ) : (
                                    news.map((article, index) => (
                                        <div key={index} className="news-article">
                                            <h3 className="article-title">{article.title}</h3>
                                            <p className="article-content">
                                                {article.description || "Telegraph transmission intercepted from frontier correspondents."}
                                            </p>
                                            <div className="article-meta">
                                                <span>📰 {article.source}</span>
                                                <span>🕐 {new Date(article.timestamp).toLocaleTimeString()}</span>
                                            </div>
                                        </div>
                                    ))
                                )}
                            </>
                        )}
                    </div>
                </div>
            );
        };
        
        // D3.js World Map Component
        const FrontierWorldMap = () => {
            const mapRef = useRef(null);
            const svgRef = useRef(null);
            const [worldData, setWorldData] = useState(null);
            const [selectedCountry, setSelectedCountry] = useState(null);
            const [news, setNews] = useState([]);
            const [loading, setLoading] = useState(false);
            const [sidebarOpen, setSidebarOpen] = useState(false);
            const [newsMarkers, setNewsMarkers] = useState([]);
            const [showActivity, setShowActivity] = useState(false);
            const newsEngine = useRef(new NewsScrapingEngine());
            
            // Load world map data and initialize D3 visualization
            useEffect(() => {
                const loadWorldData = async () => {
                    try {
                        // Load world topology data
                        const world = await d3.json('https://cdn.jsdelivr.net/npm/world-atlas@2/countries-110m.json');
                        setWorldData(world);
                    } catch (error) {
                        console.error('Failed to load world data:', error);
                        // Fallback to basic world data
                        setWorldData(createFallbackWorldData());
                    }
                };

                loadWorldData();
            }, []);

            // Initialize D3 map when world data is loaded
            useEffect(() => {
                if (!worldData || !mapRef.current) return;

                const container = mapRef.current;
                const width = container.clientWidth;
                const height = container.clientHeight;

                // Clear previous SVG
                d3.select(container).selectAll('svg').remove();

                // Create SVG
                const svg = d3.select(container)
                    .append('svg')
                    .attr('class', 'map-svg')
                    .attr('width', width)
                    .attr('height', height);

                svgRef.current = svg;

                // Create projection
                const projection = d3.geoNaturalEarth1()
                    .scale(width / 6.5)
                    .translate([width / 2, height / 2]);

                const path = d3.geoPath().projection(projection);

                // Create countries
                const countries = topojson.feature(worldData, worldData.objects.countries);

                svg.selectAll('.country-path')
                    .data(countries.features)
                    .enter()
                    .append('path')
                    .attr('class', 'country-path')
                    .attr('d', path)
                    .on('click', function(event, d) {
                        const countryName = getCountryName(d);
                        if (WORLD_COUNTRIES[countryName]) {
                            handleCountryClick(countryName, d3.select(this));
                        }
                    })
                    .on('mouseover', function(event, d) {
                        const countryName = getCountryName(d);
                        d3.select(this).style('stroke-width', '2.5px');

                        // Show tooltip
                        const tooltip = d3.select('body')
                            .append('div')
                            .attr('class', 'map-tooltip')
                            .style('position', 'absolute')
                            .style('background', 'rgba(244, 228, 188, 0.95)')
                            .style('border', '2px solid #8B4513')
                            .style('border-radius', '8px')
                            .style('padding', '10px')
                            .style('font-family', 'IM Fell English, serif')
                            .style('pointer-events', 'none')
                            .style('z-index', '1000')
                            .html(`<strong>${countryName}</strong><br><small>Click to read telegraph reports</small>`)
                            .style('left', (event.pageX + 10) + 'px')
                            .style('top', (event.pageY - 10) + 'px');
                    })
                    .on('mouseout', function() {
                        d3.select(this).style('stroke-width', '1.5px');
                        d3.selectAll('.map-tooltip').remove();
                    });

                // Add news activity markers
                if (showActivity) {
                    addNewsMarkers(svg, projection);
                }

                return () => {
                    d3.select(container).selectAll('svg').remove();
                };
            }, [worldData, showActivity]);

            // Helper functions
            const getCountryName = (countryFeature) => {
                // Try to match country names from the feature properties
                const props = countryFeature.properties;
                const possibleNames = [props.NAME, props.NAME_EN, props.ADMIN, props.NAME_LONG];

                for (const name of possibleNames) {
                    if (name && WORLD_COUNTRIES[name]) {
                        return name;
                    }
                }

                // Fallback mapping for common mismatches
                const nameMapping = {
                    'United States of America': 'United States',
                    'Russian Federation': 'Russia',
                    'People\'s Republic of China': 'China'
                };

                for (const name of possibleNames) {
                    if (name && nameMapping[name]) {
                        return nameMapping[name];
                    }
                }

                return possibleNames[0] || 'Unknown';
            };

            const handleCountryClick = async (countryName, element) => {
                // Reset previous selection
                d3.selectAll('.country-path').classed('active', false);
                element.classed('active', true);

                setSelectedCountry(countryName);
                setLoading(true);
                setSidebarOpen(true);

                try {
                    const newsData = await newsEngine.current.getNewsForCountry(countryName);
                    setNews(newsData);
                } catch (error) {
                    console.error('Error fetching news:', error);
                    setNews([]);
                } finally {
                    setLoading(false);
                }
            };

            const addNewsMarkers = (svg, projection) => {
                // Generate random news activity points
                const activityData = Object.entries(WORLD_COUNTRIES).map(([name, data]) => {
                    const coords = getCountryCoordinates(name);
                    return {
                        name,
                        coords,
                        activity: Math.random() * 10 + 1,
                        color: data.color
                    };
                });

                svg.selectAll('.news-marker')
                    .data(activityData)
                    .enter()
                    .append('circle')
                    .attr('class', 'news-marker pulsing')
                    .attr('cx', d => projection(d.coords)[0])
                    .attr('cy', d => projection(d.coords)[1])
                    .attr('r', d => Math.sqrt(d.activity) * 2)
                    .style('fill', d => d.color)
                    .on('click', function(event, d) {
                        handleCountryClick(d.name, d3.select(`.country-path`));
                    });
            };

            const getCountryCoordinates = (countryName) => {
                // Approximate coordinates for major countries
                const coords = {
                    'United States': [-95, 37],
                    'United Kingdom': [-2, 54],
                    'France': [2, 46],
                    'Germany': [10, 51],
                    'China': [104, 35],
                    'India': [77, 20],
                    'Japan': [138, 36],
                    'Australia': [133, -27],
                    'Russia': [105, 61],
                    'Brazil': [-55, -14]
                };
                return coords[countryName] || [0, 0];
            };

            const createFallbackWorldData = () => {
                // Simple fallback world data structure
                return {
                    objects: {
                        countries: {
                            geometries: Object.keys(WORLD_COUNTRIES).map((name, index) => ({
                                properties: { NAME: name },
                                id: index
                            }))
                        }
                    }
                };
            };

            const toggleActivity = () => {
                setShowActivity(!showActivity);
            };

            return (
                <div className="parchment-texture">
                    {/* Header */}
                    <header className="frontier-header">
                        <h1 className="frontier-title">
                            ⚡ FRONTIER TELEGRAPH ⚡
                        </h1>
                        <p className="frontier-subtitle">
                            World News from the Digital Frontier
                        </p>
                        <div className="telegraph-line"></div>
                    </header>

                    {/* Main Map Container */}
                    <div className="world-map-container">
                        {/* Controls Panel */}
                        <div className="controls-panel">
                            <button
                                className={`control-btn ${showActivity ? 'active' : ''}`}
                                onClick={toggleActivity}
                            >
                                📡 {showActivity ? 'Hide' : 'Show'} Telegraph Activity
                            </button>
                            <button
                                className="control-btn"
                                onClick={() => window.location.reload()}
                            >
                                🔄 Refresh Signals
                            </button>
                        </div>

                        {/* D3 Map Container */}
                        <div ref={mapRef} style={{ width: '100%', height: '100%' }} />

                        {/* Loading Overlay */}
                        {!worldData && (
                            <div style={{
                                position: 'absolute',
                                top: '50%',
                                left: '50%',
                                transform: 'translate(-50%, -50%)',
                                textAlign: 'center'
                            }}>
                                <div className="telegraph-spinner"></div>
                                <p style={{ marginTop: '1rem', fontFamily: 'IM Fell English, serif' }}>
                                    Loading world telegraph network...
                                </p>
                            </div>
                        )}
                    </div>

                    {/* Telegraph Sidebar */}
                    <TelegraphSidebar
                        isOpen={sidebarOpen}
                        selectedCountry={selectedCountry}
                        news={news}
                        loading={loading}
                        onClose={() => {
                            setSidebarOpen(false);
                            setSelectedCountry(null);
                            d3.selectAll('.country-path').classed('active', false);
                        }}
                    />

                    {/* Instructions */}
                    <div style={{
                        textAlign: 'center',
                        padding: '2rem',
                        fontStyle: 'italic',
                        color: 'var(--frontier-leather)'
                    }}>
                        <p>🗺️ Click on any territory to intercept telegraph reports from that region</p>
                        <p style={{ fontSize: '0.9rem', marginTop: '0.5rem' }}>
                            📡 Advanced web scraping technology extracts news from multiple frontier sources
                        </p>
                    </div>
                </div>
            );
        };

        // Render the application
        ReactDOM.render(<FrontierWorldMap />, document.getElementById('root'));
    </script>
</body>
</html>
