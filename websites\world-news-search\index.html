<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>World News Map - Red Dead Redemption Style</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=IM+Fell+English:ital,wght@0,400;1,400&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <!-- React and ReactDOM -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    
    <!-- Babel for JSX -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <!-- Leaflet Heat Plugin -->
    <script src="https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.js"></script>
    
    <style>
        :root {
            --rdr-parchment: #D8C7A0;
            --rdr-border: #3A2E24;
            --rdr-accent: #4E5A3E;
            --rdr-text: #2D1810;
        }
        
        body {
            font-family: 'IM Fell English', serif;
            background-color: var(--rdr-parchment);
            color: var(--rdr-text);
        }
        
        .rdr-map-container {
            border: 3px solid var(--rdr-border);
            border-radius: 8px;
            box-shadow: inset 0 0 20px rgba(58, 46, 36, 0.3);
        }
        
        .rdr-sidebar {
            background: linear-gradient(145deg, #E8D7B0, #C8B790);
            border: 2px solid var(--rdr-border);
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .rdr-button {
            background: var(--rdr-accent);
            color: var(--rdr-parchment);
            border: 2px solid var(--rdr-border);
            transition: all 0.3s ease;
        }
        
        .rdr-button:hover {
            background: var(--rdr-border);
            transform: translateY(-1px);
        }
        
        .rdr-news-item {
            border-bottom: 1px dashed var(--rdr-border);
            padding: 12px 0;
        }
        
        .rdr-news-item:last-child {
            border-bottom: none;
        }
        
        .leaflet-popup-content-wrapper {
            background: var(--rdr-parchment);
            border: 2px solid var(--rdr-border);
            border-radius: 8px;
            font-family: 'IM Fell English', serif;
        }
        
        .leaflet-popup-tip {
            background: var(--rdr-parchment);
            border: 2px solid var(--rdr-border);
        }
        
        .country-boundary {
            stroke: var(--rdr-border);
            stroke-width: 2;
            stroke-dasharray: 5,5;
            fill: transparent;
            cursor: pointer;
        }
        
        .country-boundary:hover {
            fill: rgba(78, 90, 62, 0.2);
        }
        
        .loading-spinner {
            border: 3px solid var(--rdr-parchment);
            border-top: 3px solid var(--rdr-border);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .rdr-title {
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            letter-spacing: 1px;
        }
        
        .news-date {
            color: var(--rdr-accent);
            font-style: italic;
            font-size: 0.9em;
        }
        
        .topic-tag {
            background: var(--rdr-accent);
            color: var(--rdr-parchment);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin: 2px;
            display: inline-block;
        }
    </style>
</head>
<body class="min-h-screen p-4">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useRef, useMemo } = React;
        
        // NewsAPI configuration
        const NEWS_API_KEY = 'your-api-key-here'; // Replace with actual API key
        const NEWS_API_BASE = 'https://newsapi.org/v2';
        
        // Country data with coordinates for heatmap
        const COUNTRIES = {
            'US': { name: 'United States', lat: 39.8283, lng: -98.5795, code: 'us' },
            'GB': { name: 'United Kingdom', lat: 55.3781, lng: -3.4360, code: 'gb' },
            'DE': { name: 'Germany', lat: 51.1657, lng: 10.4515, code: 'de' },
            'FR': { name: 'France', lat: 46.2276, lng: 2.2137, code: 'fr' },
            'IT': { name: 'Italy', lat: 41.8719, lng: 12.5674, code: 'it' },
            'ES': { name: 'Spain', lat: 40.4637, lng: -3.7492, code: 'es' },
            'CA': { name: 'Canada', lat: 56.1304, lng: -106.3468, code: 'ca' },
            'AU': { name: 'Australia', lat: -25.2744, lng: 133.7751, code: 'au' },
            'JP': { name: 'Japan', lat: 36.2048, lng: 138.2529, code: 'jp' },
            'CN': { name: 'China', lat: 35.8617, lng: 104.1954, code: 'cn' },
            'IN': { name: 'India', lat: 20.5937, lng: 78.9629, code: 'in' },
            'BR': { name: 'Brazil', lat: -14.2350, lng: -51.9253, code: 'br' },
            'RU': { name: 'Russia', lat: 61.5240, lng: 105.3188, code: 'ru' },
            'MX': { name: 'Mexico', lat: 23.6345, lng: -102.5528, code: 'mx' },
            'AR': { name: 'Argentina', lat: -38.4161, lng: -63.6167, code: 'ar' }
        };
        
        // Loading Spinner Component
        const LoadingSpinner = () => (
            <div className="flex justify-center items-center p-8">
                <div className="loading-spinner"></div>
            </div>
        );
        
        // News Sidebar Component
        const NewsSidebar = ({ selectedCountry, news, topics, loading, onClose }) => {
            if (!selectedCountry) return null;
            
            return (
                <div className="rdr-sidebar p-6 max-h-screen overflow-y-auto">
                    <div className="flex justify-between items-center mb-4">
                        <h2 className="text-2xl font-bold rdr-title">
                            {COUNTRIES[selectedCountry]?.name || selectedCountry}
                        </h2>
                        <button 
                            onClick={onClose}
                            className="rdr-button px-3 py-1 rounded text-sm"
                        >
                            ✕
                        </button>
                    </div>
                    
                    {loading ? (
                        <LoadingSpinner />
                    ) : (
                        <>
                            {topics.length > 0 && (
                                <div className="mb-6">
                                    <h3 className="text-lg font-semibold mb-3">Common Topics</h3>
                                    <div className="flex flex-wrap">
                                        {topics.map((topic, index) => (
                                            <span key={index} className="topic-tag">
                                                {topic}
                                            </span>
                                        ))}
                                    </div>
                                </div>
                            )}
                            
                            <div>
                                <h3 className="text-lg font-semibold mb-3">Latest News</h3>
                                {news.length === 0 ? (
                                    <p className="text-gray-600 italic">No news articles available</p>
                                ) : (
                                    news.map((article, index) => (
                                        <div key={index} className="rdr-news-item">
                                            <h4 className="font-semibold mb-2 leading-tight">
                                                <a 
                                                    href={article.url} 
                                                    target="_blank" 
                                                    rel="noopener noreferrer"
                                                    className="hover:underline"
                                                >
                                                    {article.title}
                                                </a>
                                            </h4>
                                            <p className="text-sm mb-2 leading-relaxed">
                                                {article.description}
                                            </p>
                                            <div className="flex justify-between items-center text-xs">
                                                <span className="font-medium">{article.source.name}</span>
                                                <span className="news-date">
                                                    {new Date(article.publishedAt).toLocaleDateString()}
                                                </span>
                                            </div>
                                        </div>
                                    ))
                                )}
                            </div>
                        </>
                    )}
                </div>
            );
        };
        
        // Main World News Map Component
        const WorldNewsMap = () => {
            const mapRef = useRef(null);
            const leafletMapRef = useRef(null);
            const heatmapLayerRef = useRef(null);
            
            const [selectedCountry, setSelectedCountry] = useState(null);
            const [news, setNews] = useState([]);
            const [topics, setTopics] = useState([]);
            const [loading, setLoading] = useState(false);
            const [showHeatmap, setShowHeatmap] = useState(false);
            const [newsCache, setNewsCache] = useState({});
            
            // Initialize map
            useEffect(() => {
                if (!mapRef.current || leafletMapRef.current) return;
                
                // Create map with Stamen Watercolor tiles for RDR2 aesthetic
                const map = L.map(mapRef.current, {
                    center: [20, 0],
                    zoom: 2,
                    minZoom: 2,
                    maxZoom: 6,
                    zoomControl: true
                });
                
                // Add base layer with watercolor effect
                L.tileLayer('https://stamen-tiles.a.ssl.fastly.net/watercolor/{z}/{x}/{y}.jpg', {
                    attribution: '&copy; <a href="http://stamen.com">Stamen Design</a>',
                    maxZoom: 6
                }).addTo(map);
                
                leafletMapRef.current = map;
                
                // Add country markers
                Object.entries(COUNTRIES).forEach(([code, country]) => {
                    const marker = L.circleMarker([country.lat, country.lng], {
                        radius: 8,
                        fillColor: '#4E5A3E',
                        color: '#3A2E24',
                        weight: 2,
                        opacity: 1,
                        fillOpacity: 0.8
                    }).addTo(map);
                    
                    marker.bindPopup(`
                        <div style="font-family: 'IM Fell English', serif;">
                            <strong>${country.name}</strong><br>
                            <small>Click to view news</small>
                        </div>
                    `);
                    
                    marker.on('click', () => {
                        setSelectedCountry(code);
                        fetchNews(country.code);
                    });
                });
                
                return () => {
                    if (leafletMapRef.current) {
                        leafletMapRef.current.remove();
                        leafletMapRef.current = null;
                    }
                };
            }, []);

            // Fetch news for selected country
            const fetchNews = async (countryCode) => {
                setLoading(true);

                // Check cache first
                if (newsCache[countryCode]) {
                    setNews(newsCache[countryCode].articles);
                    setTopics(newsCache[countryCode].topics);
                    setLoading(false);
                    return;
                }

                try {
                    // Simulate API call with mock data for demo
                    // Replace with actual NewsAPI call:
                    // const response = await fetch(`${NEWS_API_BASE}/top-headlines?country=${countryCode}&apiKey=${NEWS_API_KEY}`);

                    // Mock data for demonstration
                    const mockNews = {
                        articles: [
                            {
                                title: `Breaking News from ${COUNTRIES[Object.keys(COUNTRIES).find(k => COUNTRIES[k].code === countryCode)]?.name}`,
                                description: "This is a sample news article description that would normally come from the NewsAPI.",
                                url: "#",
                                source: { name: "Sample News" },
                                publishedAt: new Date().toISOString()
                            },
                            {
                                title: "Economic Update",
                                description: "Latest economic developments and market trends affecting the region.",
                                url: "#",
                                source: { name: "Financial Times" },
                                publishedAt: new Date(Date.now() - 86400000).toISOString()
                            },
                            {
                                title: "Political Developments",
                                description: "Recent political changes and their implications for the country.",
                                url: "#",
                                source: { name: "Political Review" },
                                publishedAt: new Date(Date.now() - 172800000).toISOString()
                            }
                        ]
                    };

                    // Extract topics from titles and descriptions
                    const allText = mockNews.articles.map(a => `${a.title} ${a.description}`).join(' ');
                    const commonWords = ['politics', 'economy', 'business', 'technology', 'health', 'environment'];
                    const foundTopics = commonWords.filter(word =>
                        allText.toLowerCase().includes(word)
                    );

                    const newsData = {
                        articles: mockNews.articles,
                        topics: foundTopics.length > 0 ? foundTopics : ['general', 'news']
                    };

                    // Cache the results
                    setNewsCache(prev => ({
                        ...prev,
                        [countryCode]: newsData
                    }));

                    setNews(newsData.articles);
                    setTopics(newsData.topics);

                } catch (error) {
                    console.error('Error fetching news:', error);
                    setNews([]);
                    setTopics(['error']);
                } finally {
                    setLoading(false);
                }
            };

            // Toggle heatmap
            const toggleHeatmap = () => {
                if (!leafletMapRef.current) return;

                if (showHeatmap && heatmapLayerRef.current) {
                    leafletMapRef.current.removeLayer(heatmapLayerRef.current);
                    heatmapLayerRef.current = null;
                } else if (!showHeatmap) {
                    // Create heatmap data based on country activity
                    const heatmapData = Object.values(COUNTRIES).map(country => [
                        country.lat,
                        country.lng,
                        Math.random() * 0.8 + 0.2 // Random intensity for demo
                    ]);

                    heatmapLayerRef.current = L.heatLayer(heatmapData, {
                        radius: 50,
                        blur: 35,
                        maxZoom: 6,
                        gradient: {
                            0.0: '#4E5A3E',
                            0.5: '#8B7355',
                            1.0: '#D8C7A0'
                        }
                    }).addTo(leafletMapRef.current);
                }

                setShowHeatmap(!showHeatmap);
            };

            return (
                <div className="min-h-screen">
                    <header className="text-center mb-6">
                        <h1 className="text-4xl font-bold rdr-title mb-2">
                            World News Map
                        </h1>
                        <p className="text-lg italic">
                            Explore global news in the style of the Old West
                        </p>
                    </header>

                    <div className="flex flex-col lg:flex-row gap-6 h-full">
                        {/* Map Container */}
                        <div className="flex-1">
                            <div className="mb-4 flex justify-center">
                                <button
                                    onClick={toggleHeatmap}
                                    className={`rdr-button px-6 py-2 rounded-lg font-semibold ${
                                        showHeatmap ? 'bg-opacity-80' : ''
                                    }`}
                                >
                                    {showHeatmap ? 'Hide' : 'Show'} News Activity Heatmap
                                </button>
                            </div>

                            <div
                                ref={mapRef}
                                className="rdr-map-container w-full h-96 lg:h-[600px]"
                            />

                            <div className="mt-4 text-center text-sm italic">
                                Click on country markers to view news and topics
                            </div>
                        </div>

                        {/* Sidebar */}
                        {selectedCountry && (
                            <div className="lg:w-96">
                                <NewsSidebar
                                    selectedCountry={selectedCountry}
                                    news={news}
                                    topics={topics}
                                    loading={loading}
                                    onClose={() => setSelectedCountry(null)}
                                />
                            </div>
                        )}
                    </div>

                    <footer className="mt-8 text-center text-sm">
                        <p className="italic">
                            Built with React, Leaflet, and the spirit of the frontier
                        </p>
                        <p className="mt-2 text-xs">
                            Note: Replace NEWS_API_KEY with your actual NewsAPI key for live data
                        </p>
                    </footer>
                </div>
            );
        };

        // Render the application
        ReactDOM.render(<WorldNewsMap />, document.getElementById('root'));
    </script>
</body>
</html>
